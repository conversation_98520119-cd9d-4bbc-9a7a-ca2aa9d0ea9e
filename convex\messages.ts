import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { api } from "./_generated/api";

// Crear un nuevo mensaje/lead
export const createMessage = mutation({
  args: {
    propertyId: v.id("properties"),
    subject: v.string(),
    message: v.string(),
    senderName: v.string(),
    senderEmail: v.string(),
    senderPhone: v.optional(v.string()),
    leadType: v.union(
      v.literal("inquiry"),
      v.literal("viewing"), 
      v.literal("offer"),
      v.literal("negotiation")
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Debes estar autenticado para enviar mensajes");
    }

    // Obtener la propiedad para saber quién es el destinatario
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // Crear el mensaje
    const messageId = await ctx.db.insert("messages", {
      propertyId: args.propertyId,
      subject: args.subject,
      message: args.message,
      senderId: identity.subject,
      receiverId: property.ownerId,
      status: "unread",
      leadType: args.leadType,
      senderName: args.senderName,
      senderEmail: args.senderEmail,
      senderPhone: args.senderPhone,
      createdAt: new Date().toISOString(),
      // Para futuro: calcular créditos según el tipo de lead
      creditsCharged: getCreditsForLeadType(args.leadType),
      creditsPaid: false,
    });

    // Consumir créditos del propietario que recibe la consulta
    try {
      // Buscar al propietario para consumir sus créditos
      const ownerIdentity = { subject: property.ownerId }; // Simular identidad del propietario
      
      // Verificar límites primero
      const subscription = await ctx.db
        .query("subscriptions")
        .withIndex("userId", (q) => q.eq("userId", property.ownerId))
        .first();

      let creditsToConsume = getCreditsForLeadType(args.leadType);

      if (!subscription) {
        // Crear suscripción gratuita si no existe
        await ctx.db.insert("subscriptions", {
          userId: property.ownerId,
          plan: "free",
          status: "active",
          credits: 10,
          maxProperties: 5,
          creditsUsed: creditsToConsume,
          propertiesCount: 0,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      } else {
        // Actualizar créditos usados
        await ctx.db.patch(subscription._id, {
          creditsUsed: subscription.creditsUsed + creditsToConsume,
          updatedAt: Date.now(),
        });
      }

          // Marcar que los créditos fueron cobrados
    await ctx.db.patch(messageId, {
      creditsPaid: true,
    });

    // 📧 ENVIAR NOTIFICACIONES POR EMAIL
    try {
      // Obtener información del propietario
      const owner = await ctx.db
        .query("users")
        .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
        .first();

      // Enviar notificación al propietario
      if (owner && owner.email) {
        ctx.scheduler.runAfter(0, api.emails.notifyWebMessageReceived, {
          ownerEmail: owner.email,
          ownerName: owner.name || "Propietario",
          senderName: args.senderName,
          senderEmail: args.senderEmail,
          senderPhone: args.senderPhone,
          propertyTitle: property.title,
          propertyAddress: `${property.address}${property.location?.level2?.name ? `, ${property.location.level2.name}` : ''}`,
          propertyPrice: `${property.currency} ${property.price?.toLocaleString()}`,
          subject: args.subject,
          message: args.message,
          leadType: args.leadType,
          createdAt: new Date().toISOString(),
          dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/messages`,
        }).catch((error) => {
          console.error("Error enviando notificación al propietario:", error);
        });
      }

      // Enviar confirmación al comprador
      ctx.scheduler.runAfter(0, api.emails.notifyMessageConfirmation, {
        senderName: args.senderName,
        senderEmail: args.senderEmail,
        propertyTitle: property.title,
        propertyAddress: `${property.address}${property.location?.level2?.name ? `, ${property.location.level2.name}` : ''}`,
        propertyPrice: `${property.currency} ${property.price?.toLocaleString()}`,
        subject: args.subject,
        message: args.message,
      }).catch((error) => {
        console.error("Error enviando confirmación al comprador:", error);
      });

    } catch (error) {
      console.error("Error programando notificaciones de email:", error);
    }

    // 🚨 VERIFICAR CRÉDITOS AGOTADOS Y NOTIFICAR
    if (subscription) {
      try {
        const updatedSubscription = await ctx.db.get(subscription._id);
        if (updatedSubscription) {
          const remainingCredits = updatedSubscription.credits - updatedSubscription.creditsUsed;
          
          // Notificar cuando los créditos se agotan completamente
          if (remainingCredits <= 0) {
            const owner = await ctx.db
              .query("users")
              .withIndex("by_token", (q) => q.eq("tokenIdentifier", property.ownerId))
              .first();

            if (owner && owner.email) {
              ctx.scheduler.runAfter(0, api.emails.notifyLowCredits, {
                userEmail: owner.email,
                userName: owner.name || "Usuario",
                currentPlan: updatedSubscription.plan,
                creditsUsed: updatedSubscription.creditsUsed,
                totalCredits: updatedSubscription.credits,
                dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/finance`,
                upgradeUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/finance`,
              }).catch((error) => {
                console.error("Error enviando notificación de créditos agotados:", error);
              });
            }
          }
        }
      } catch (error) {
        console.error("Error verificando créditos agotados:", error);
      }
    }

  } catch (error) {
    console.error("Error consumiendo créditos:", error);
    // No fallar la creación del mensaje por errores de créditos
  }

  return messageId;
  },
});

// Función helper para calcular créditos (sistema de moneda interna)
function getCreditsForLeadType(leadType: string): number {
  switch (leadType) {
    case "inquiry": return 1;      // Consulta básica
    case "viewing": return 3;      // Solicitud de visita (más valiosa)
    case "offer": return 5;        // Oferta de compra (muy valiosa)
    case "negotiation": return 2;  // Negociación
    default: return 1;
  }
}

// Obtener mensajes recibidos (bandeja de entrada)
export const getInboxMessages = query({
  args: {
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    let query = ctx.db
      .query("messages")
      .withIndex("by_receiver", (q) => q.eq("receiverId", identity.subject));

    if (args.status) {
      query = query.filter((q: any) => q.eq(q.field("status"), args.status));
    }

    const messages = await query
      .order("desc")
      .take(args.limit || 50);

    // Enriquecer con datos de la propiedad
    const enrichedMessages = await Promise.all(
      messages.map(async (message) => {
        const property = await ctx.db.get(message.propertyId);
        return {
          ...message,
          property: property ? {
            title: property.title,
            images: property.images,
            price: property.price,
            currency: property.currency,
            type: property.type,
            city: property.location?.level2?.name || '',
          } : null,
        };
      })
    );

    return enrichedMessages;
  },
});

// Obtener mensajes enviados
export const getSentMessages = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_sender", (q) => q.eq("senderId", identity.subject))
      .order("desc")
      .take(args.limit || 50);

    // Enriquecer con datos de la propiedad
    const enrichedMessages = await Promise.all(
      messages.map(async (message) => {
        const property = await ctx.db.get(message.propertyId);
        return {
          ...message,
          property: property ? {
            title: property.title,
            images: property.images,
            price: property.price,
            currency: property.currency,
            type: property.type,
            city: property.location?.level2?.name || '',
          } : null,
        };
      })
    );

    return enrichedMessages;
  },
});

// Marcar mensaje como leído
export const markAsRead = mutation({
  args: {
    messageId: v.id("messages"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Mensaje no encontrado");
    }

    // Solo el destinatario puede marcar como leído
    if (message.receiverId !== identity.subject) {
      throw new Error("No autorizado");
    }

    await ctx.db.patch(args.messageId, {
      status: "read",
      readAt: new Date().toISOString(),
    });

    return args.messageId;
  },
});

// Obtener estadísticas de mensajes
export const getMessageStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const allMessages = await ctx.db
      .query("messages")
      .withIndex("by_receiver", (q) => q.eq("receiverId", identity.subject))
      .collect();

    const unreadCount = allMessages.filter(m => m.status === "unread").length;
    const totalCount = allMessages.length;
    const creditsEarned = allMessages.reduce((sum, m) => sum + (m.creditsCharged || 0), 0);

    const leadTypeStats = allMessages.reduce((acc, message) => {
      acc[message.leadType] = (acc[message.leadType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      unreadCount,
      totalCount,
      creditsEarned,
      leadTypeStats,
    };
  },
});

// Archivar mensaje
export const archiveMessage = mutation({
  args: {
    messageId: v.id("messages"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Mensaje no encontrado");
    }

    // Solo el destinatario puede archivar
    if (message.receiverId !== identity.subject) {
      throw new Error("No autorizado");
    }

    await ctx.db.patch(args.messageId, {
      status: "archived",
    });

    return args.messageId;
  },
});

// Responder a un mensaje
export const replyToMessage = mutation({
  args: {
    originalMessageId: v.id("messages"),
    replyMessage: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Obtener el mensaje original
    const originalMessage = await ctx.db.get(args.originalMessageId);
    if (!originalMessage) {
      throw new Error("Mensaje original no encontrado");
    }

    // Solo el destinatario puede responder
    if (originalMessage.receiverId !== identity.subject) {
      throw new Error("No autorizado para responder este mensaje");
    }

    // Obtener información del propietario (quien responde)
    const owner = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    // Obtener información de la propiedad
    const property = await ctx.db.get(originalMessage.propertyId);
    if (!property) {
      throw new Error("Propiedad no encontrada");
    }

    // Marcar el mensaje original como respondido
    await ctx.db.patch(args.originalMessageId, {
      status: "replied",
      repliedAt: new Date().toISOString(),
    });

    // Crear un nuevo mensaje de respuesta (opcional, para historial)
    const replyMessageId = await ctx.db.insert("messages", {
      propertyId: originalMessage.propertyId,
      subject: `Re: ${originalMessage.subject}`,
      message: args.replyMessage,
      senderId: identity.subject, // El propietario que responde
      receiverId: originalMessage.senderId, // El cliente original
      status: "read", // La respuesta se marca como leída por defecto
      leadType: originalMessage.leadType,
      senderName: owner?.name || "Propietario",
      senderEmail: owner?.email || "",
      senderPhone: owner?.phone,
      createdAt: new Date().toISOString(),
      creditsCharged: 0, // Las respuestas no cobran créditos
      creditsPaid: true,
      isReply: true,
      originalMessageId: args.originalMessageId,
    });

    // Enviar notificación por email al cliente
    if (originalMessage.senderEmail) {
      ctx.scheduler.runAfter(0, api.emails.notifyMessageReply, {
        clientEmail: originalMessage.senderEmail,
        clientName: originalMessage.senderName,
        ownerName: owner?.name || "Propietario",
        ownerEmail: owner?.email || "",
        ownerPhone: owner?.phone,
        propertyTitle: property.title,
        propertyAddress: `${property.address}${property.location?.level2?.name ? `, ${property.location.level2.name}` : ''}`,
        propertyPrice: `${property.currency} ${property.price?.toLocaleString()}`,
        propertyUrl: `${process.env.NEXT_PUBLIC_APP_URL}/property/${property._id}`,
        originalSubject: originalMessage.subject,
        originalMessage: originalMessage.message,
        replyMessage: args.replyMessage,
        replyDate: new Date().toISOString(),
      }).catch((error) => {
        console.error("Error enviando notificación de respuesta:", error);
      });
    }

    return {
      originalMessageId: args.originalMessageId,
      replyMessageId,
      emailSent: !!originalMessage.senderEmail,
    };
  },
});
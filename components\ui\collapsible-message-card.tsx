"use client";

import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronUp, Calendar, Building2, User, Mail } from 'lucide-react';
import Image from "next/image";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

interface CollapsibleMessageCardProps {
  message: any;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  showPreview?: boolean;
}

export function CollapsibleMessageCard({
  message,
  children,
  defaultExpanded = false,
  showPreview = true
}: CollapsibleMessageCardProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const getLeadTypeBadge = (leadType: string) => {
    switch (leadType) {
      case 'inquiry':
        return { label: 'Consulta', color: 'bg-blue-100 text-blue-800' };
      case 'viewing':
        return { label: 'Visita', color: 'bg-green-100 text-green-800' };
      case 'offer':
        return { label: 'Oferta', color: 'bg-purple-100 text-purple-800' };
      case 'negotiation':
        return { label: 'Negociación', color: 'bg-orange-100 text-orange-800' };
      default:
        return { label: 'General', color: 'bg-gray-100 text-gray-800' };
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('es-GT', {
      style: 'currency',
      currency: currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Vista contraída (resumen)
  if (!isExpanded && showPreview) {
    return (
      <Card 
        className="hover:shadow-md transition-shadow cursor-pointer border-l-4 border-l-blue-400"
        onClick={() => setIsExpanded(true)}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              {/* Avatar/Imagen pequeña */}
              <div className="w-10 h-10 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                {message.property?.images?.[0] ? (
                  <Image
                    src={message.property.images[0]}
                    alt={message.property.title}
                    width={40}
                    height={40}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Building2 className="h-5 w-5 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Información resumida */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-gray-900 truncate">
                    {message.subject}
                  </h4>
                  <Badge className={getLeadTypeBadge(message.leadType).color}>
                    {getLeadTypeBadge(message.leadType).label}
                  </Badge>
                  {message.status === 'unread' && (
                    <Badge variant="default" className="bg-blue-600">
                      Nuevo
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span className="flex items-center gap-1">
                    <User className="w-3 h-3" />
                    {message.senderName}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {formatDistanceToNow(new Date(message.createdAt), { 
                      addSuffix: true, 
                      locale: es 
                    })}
                  </span>
                  {message.property && (
                    <span className="flex items-center gap-1 truncate">
                      <Building2 className="w-3 h-3" />
                      {message.property.title}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Botón expandir */}
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-6 w-6 p-0 flex-shrink-0"
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(true);
              }}
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Vista expandida (completa)
  return (
    <div className="relative">
      {/* Botón contraer en la esquina superior derecha */}
      <Button
        variant="ghost"
        size="sm"
        className="absolute top-2 right-2 z-10 h-6 w-6 p-0 bg-white/80 hover:bg-white shadow-sm"
        onClick={() => setIsExpanded(false)}
      >
        <ChevronUp className="h-4 w-4" />
      </Button>
      
      {/* Contenido completo */}
      {children}
    </div>
  );
}

// Hook para manejar múltiples mensajes colapsables
export function useCollapsibleMessages(messageIds: string[], defaultExpanded = false) {
  const [expandedStates, setExpandedStates] = useState<Record<string, boolean>>(
    messageIds.reduce((acc, id) => ({
      ...acc,
      [id]: defaultExpanded
    }), {})
  );

  const toggleMessage = (messageId: string) => {
    setExpandedStates(prev => ({
      ...prev,
      [messageId]: !prev[messageId]
    }));
  };

  const expandAll = () => {
    setExpandedStates(prev => 
      Object.keys(prev).reduce((acc, id) => ({
        ...acc,
        [id]: true
      }), {})
    );
  };

  const collapseAll = () => {
    setExpandedStates(prev => 
      Object.keys(prev).reduce((acc, id) => ({
        ...acc,
        [id]: false
      }), {})
    );
  };

  return {
    expandedStates,
    toggleMessage,
    expandAll,
    collapseAll,
    isExpanded: (messageId: string) => expandedStates[messageId] ?? defaultExpanded
  };
}

// Componente de control para expandir/contraer todos
export function MessageListControls({
  onExpandAll,
  onCollapseAll,
  totalCount,
  expandedCount
}: {
  onExpandAll: () => void;
  onCollapseAll: () => void;
  totalCount: number;
  expandedCount: number;
}) {
  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <span className="text-sm text-gray-600">
        {expandedCount} de {totalCount} mensajes expandidos
      </span>
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onExpandAll}
          disabled={expandedCount === totalCount}
        >
          Expandir todos
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onCollapseAll}
          disabled={expandedCount === 0}
        >
          Contraer todos
        </Button>
      </div>
    </div>
  );
}

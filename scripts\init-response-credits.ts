/**
 * Script para inicializar las nuevas acciones de créditos del sistema "pago por respuesta"
 * 
 * Ejecutar con: npx convex run scripts/init-response-credits:initializeResponseCredits
 */

import { api } from "../convex/_generated/api";

export default async function initializeResponseCredits() {
  console.log("🚀 Inicializando sistema de 'pago por respuesta'...");
  
  try {
    // Agregar las nuevas configuraciones de créditos
    const result = await api.subscriptions.addMissingCreditActions();
    
    console.log("✅ Resultado:", result);
    
    if (result.inserted > 0) {
      console.log(`✅ Se agregaron ${result.inserted} nuevas configuraciones de créditos:`);
      console.log("   - respond_to_message (2 créditos)");
      console.log("   - respond_to_appointment (3 créditos)");
    } else {
      console.log("ℹ️ Todas las configuraciones ya existían");
    }
    
    console.log("\n🎯 Sistema 'pago por respuesta' configurado correctamente!");
    console.log("\n📋 Funcionalidades habilitadas:");
    console.log("   • Mensajes: Vista limitada hasta responder");
    console.log("   • Citas: Vista limitada hasta aprobar/rechazar");
    console.log("   • Créditos se consumen solo al responder");
    console.log("   • Información completa se desbloquea post-respuesta");
    
  } catch (error) {
    console.error("❌ Error al inicializar:", error);
  }
}

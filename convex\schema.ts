import { defineSchema, defineTable } from "convex/server"
import { Infer, v } from "convex/values"

export const INTERVALS = {
    MONTH: "month",
    YEAR: "year",
} as const;

export const intervalValidator = v.union(
    v.literal(INTERVALS.MONTH),
    v.literal(INTERVALS.YEAR),
);

export type Interval = Infer<typeof intervalValidator>;

// Location hierarchy validators for flexible geographic data
export const locationHierarchyValidator = v.object({
  country: v.object({
    code: v.string(), // ISO 3166-1 (GT, MX, US, etc.)
    name: v.string(), // "Guatemala", "México", "Estados Unidos"
  }),
  level1: v.optional(v.object({ // Departamento/Estado/Provincia
    code: v.optional(v.string()),
    name: v.string(),
    type: v.string(), // "departamento", "estado", "provincia"
  })),
  level2: v.optional(v.object({ // Municipio/Ciudad/Condado
    code: v.optional(v.string()),
    name: v.string(),
    type: v.string(), // "municipio", "ciudad", "condado"
  })),
  level3: v.optional(v.object({ // Zona/Distrito/Barrio
    code: v.optional(v.string()),
    name: v.string(),
    type: v.string(), // "zona", "distrito", "barrio"
  })),
  level4: v.optional(v.object({ // Colonia/Vecindario/Sector
    code: v.optional(v.string()),
    name: v.string(),
    type: v.string(), // "colonia", "vecindario", "sector"
  })),
  level5: v.optional(v.object({ // Manzana/Bloque
    code: v.optional(v.string()),
    name: v.string(),
    type: v.string(), // "manzana", "bloque", "cuadra"
  })),
  level6: v.optional(v.object({ // Calle/Subdivisión
    code: v.optional(v.string()),
    name: v.string(),
    type: v.string(), // "calle", "subdivision", "lote"
  })),
});

// Define a price object structure that matches your data
const priceValidator = v.object({
    amount: v.number(),
    polarId: v.string(),
});

// Define a prices object structure for a specific interval
const intervalPricesValidator = v.object({
    usd: priceValidator,
});

// Property validators
export const PROPERTY_TYPES = {
    HOUSE: "house",
    APARTMENT: "apartment", 
    OFFICE: "office",
    LAND: "land",
    COMMERCIAL: "commercial"
} as const;

export const PROPERTY_STATUS = {
    FOR_SALE: "for_sale",
    FOR_RENT: "for_rent", 
    SOLD: "sold",
    RENTED: "rented",
    DRAFT: "draft"
} as const;

export const USER_ROLES = {
    BUYER: "buyer",
    SELLER: "seller",
    AGENT: "agent", 
    ADMIN: "admin"
} as const;

export default defineSchema({
    users: defineTable({
        createdAt: v.string(),
        email: v.string(),
        name: v.optional(v.string()),
        image: v.optional(v.string()),
        userId: v.string(),
        subscription: v.optional(v.string()),
        credits: v.optional(v.string()),
        tokenIdentifier: v.string(),
        // Nuevos campos para marketplace
        role: v.optional(v.union(
            v.literal(USER_ROLES.BUYER),
            v.literal(USER_ROLES.SELLER), 
            v.literal(USER_ROLES.AGENT),
            v.literal(USER_ROLES.ADMIN)
        )),
        phone: v.optional(v.string()),
        company: v.optional(v.string()),
        license: v.optional(v.string()), // para agentes
        bio: v.optional(v.string()),
        avatar: v.optional(v.string()),
        currency: v.optional(v.string()),
        notifications: v.optional(v.boolean()),
        newsletter: v.optional(v.boolean()),
        // Preferencias de ubicación
        defaultCountry: v.optional(v.string()), // Código de país por defecto (GT, MX, etc.)
        preferredLanguage: v.optional(v.string()), // es, en, etc.
    }).index("by_token", ["tokenIdentifier"])
      .index("by_role", ["role"]),

    properties: defineTable({
        title: v.string(),
        description: v.string(),
        price: v.number(),
        currency: v.string(),
        type: v.union(
            v.literal(PROPERTY_TYPES.HOUSE),
            v.literal(PROPERTY_TYPES.APARTMENT),
            v.literal(PROPERTY_TYPES.OFFICE), 
            v.literal(PROPERTY_TYPES.LAND),
            v.literal(PROPERTY_TYPES.COMMERCIAL)
        ),
        status: v.union(
            v.literal(PROPERTY_STATUS.FOR_SALE),
            v.literal(PROPERTY_STATUS.FOR_RENT),
            v.literal(PROPERTY_STATUS.SOLD),
            v.literal(PROPERTY_STATUS.RENTED),
            v.literal(PROPERTY_STATUS.DRAFT)
        ),
        
        // Ubicación (nueva estructura jerárquica)
        location: v.optional(locationHierarchyValidator),
        address: v.string(),
        coordinates: v.optional(v.object({
            lat: v.number(),
            lng: v.number(),
            accuracy: v.optional(v.string()), // "exact", "approximate", "city"
        })),

        // Metadatos de ubicación para búsqueda semántica
        locationMetadata: v.optional(v.object({
            searchKeywords: v.array(v.string()), // ["zona 10", "guatemala city", "centro"]
            landmarks: v.optional(v.array(v.string())), // ["Torre del Reformador", "CC Pradera"]
            accessibility: v.optional(v.array(v.string())), // ["metro", "bus", "carretera"]
            neighborhood: v.optional(v.string()), // Descripción del vecindario
        })),


        // Características
        bedrooms: v.optional(v.number()),
        bathrooms: v.optional(v.number()),
        area: v.number(), // m²
        builtYear: v.optional(v.number()),
        parking: v.optional(v.number()),
        
        // Amenidades
        amenities: v.optional(v.array(v.string())),
        
        // Media
        images: v.array(v.string()), // URLs Cloudinary
        virtualTour: v.optional(v.string()),
        floorPlan: v.optional(v.string()),
        
        // Relaciones
        ownerId: v.string(), // Usuario vendedor
        agentId: v.optional(v.string()), // Agente inmobiliario
        
        // Timestamps
        createdAt: v.string(),
        updatedAt: v.string(),
        publishedAt: v.optional(v.string()),
        
        // SEO
        slug: v.optional(v.string()),
        featured: v.optional(v.boolean()),
        
        // Sistema de créditos y destacadas
        featuredUntil: v.optional(v.number()), // timestamp hasta cuándo es destacada
        premiumHomeUntil: v.optional(v.number()), // timestamp hasta cuándo está en home premium
        featuredTransaction: v.optional(v.id("transactions")), // referencia a la transacción
        premiumTransaction: v.optional(v.id("transactions")), // referencia a la transacción premium
        
        // CAMPOS ELIMINADOS: embedding, embeddingText, embeddingUpdatedAt
        // Estos campos eran parte del sistema de búsqueda semántica eliminado

        // Estadísticas de la propiedad
        views: v.optional(v.number()), // número de vistas de la propiedad
        favoritesCount: v.optional(v.number()) // número de veces que se ha marcado como favorita
    })
        .index("by_owner", ["ownerId"])
        .index("by_agent", ["agentId"])
        .index("by_type", ["type"])
        .index("by_status", ["status"])
        .index("by_price", ["price"])
        .index("by_featured", ["featured"])
        .index("by_published", ["publishedAt"])
        .index("by_featured_until", ["featuredUntil"])
        .index("by_premium_until", ["premiumHomeUntil"]),

    favorites: defineTable({
        userId: v.string(),
        propertyId: v.id("properties"),
        createdAt: v.string(),
    })
        .index("by_user", ["userId"])
        .index("by_property", ["propertyId"])
        .index("by_user_property", ["userId", "propertyId"]),

    inquiries: defineTable({
        propertyId: v.id("properties"),
        buyerId: v.string(),
        sellerId: v.string(),
        message: v.string(),
        phone: v.optional(v.string()),
        email: v.string(),
        status: v.union(
            v.literal("pending"),
            v.literal("responded"), 
            v.literal("closed")
        ),
        createdAt: v.string(),
        respondedAt: v.optional(v.string()),
        response: v.optional(v.string()),
    })
        .index("by_property", ["propertyId"])
        .index("by_buyer", ["buyerId"])
        .index("by_seller", ["sellerId"])
        .index("by_status", ["status"]),

    // Sistema de suscripciones mejorado para Stripe
    subscriptions: defineTable({
        userId: v.string(),

        // Información de Stripe (reemplaza Polar)
        stripeSubscriptionId: v.optional(v.string()),
        stripeCustomerId: v.optional(v.string()),
        stripeSessionId: v.optional(v.string()),
        stripePriceId: v.optional(v.string()),

        // Compatibilidad con datos existentes de Polar (opcional)
        polarSubscriptionId: v.optional(v.string()),
        polarCustomerId: v.optional(v.string()),
        polarId: v.optional(v.string()),
        polarPriceId: v.optional(v.string()),

        // Plan y límites - Freemium: free, pro y premium
        plan: v.union(
            v.literal("free"),
            v.literal("pro"),
            v.literal("premium")
        ),
        status: v.union(
            v.literal("active"),
            v.literal("canceled"),
            v.literal("past_due"),
            v.literal("unpaid"),
            v.literal("incomplete"),
            v.literal("trialing")
        ),

        // NUEVOS CAMPOS PARA TRIAL SYSTEM
        isTrialActive: v.optional(v.boolean()),
        trialStartDate: v.optional(v.number()),
        trialEndDate: v.optional(v.number()),
        trialDaysGranted: v.optional(v.number()),
        hasUsedTrial: v.optional(v.boolean()),
        userRole: v.optional(v.union(v.literal("seller"), v.literal("agent"))),

        // CAMPOS PARA RECORDATORIOS
        trialReminderSent: v.optional(v.boolean()),
        expirationReminderSent: v.optional(v.boolean()),
        
        // Límites del plan
        credits: v.number(), // Consultas permitidas por mes
        creditsUsed: v.number(), // Consultas usadas en el período actual
        maxProperties: v.number(), // Propiedades máximas permitidas
        propertiesCount: v.number(), // Propiedades actuales del usuario
        
        // Información de facturación
        currency: v.optional(v.string()),
        interval: v.optional(v.string()),
        amount: v.optional(v.number()),
        
        // Períodos
        currentPeriodStart: v.optional(v.number()),
        currentPeriodEnd: v.optional(v.number()),
        cancelAtPeriodEnd: v.optional(v.boolean()),
        
        // Fechas importantes
        startedAt: v.optional(v.number()),
        endsAt: v.optional(v.number()),
        endedAt: v.optional(v.number()),
        canceledAt: v.optional(v.number()),
        
        // Motivos de cancelación
        customerCancellationReason: v.optional(v.string()),
        customerCancellationComment: v.optional(v.string()),
        
        // Metadata adicional
        metadata: v.optional(v.any()),
        customFieldData: v.optional(v.any()),
        
        // Timestamps
        createdAt: v.number(),
        updatedAt: v.number(),
    })
        .index("userId", ["userId"])
        .index("stripeSubscriptionId", ["stripeSubscriptionId"])
        .index("stripeCustomerId", ["stripeCustomerId"])
        // Mantener índices de Polar para compatibilidad
        .index("polarSubscriptionId", ["polarSubscriptionId"])
        .index("polarId", ["polarId"])
        .index("by_plan", ["plan"])
        .index("by_status", ["status"]),

    // NUEVA TABLA: Configuración de países para ubicación
    locationConfig: defineTable({
        countryCode: v.string(), // ISO 3166-1
        countryName: v.string(),
        hierarchy: v.object({
            level1: v.object({
                name: v.string(), // "Departamento", "Estado", "Provincia"
                required: v.boolean(),
                hasCode: v.boolean(),
            }),
            level2: v.object({
                name: v.string(), // "Municipio", "Ciudad", "Condado"
                required: v.boolean(),
                hasCode: v.boolean(),
            }),
            level3: v.optional(v.object({
                name: v.string(), // "Zona", "Distrito", "Barrio"
                required: v.boolean(),
                hasCode: v.boolean(),
            })),
            level4: v.optional(v.object({
                name: v.string(), // "Colonia", "Vecindario", "Sector"
                required: v.boolean(),
                hasCode: v.boolean(),
            })),
            level5: v.optional(v.object({
                name: v.string(), // "Manzana", "Bloque", "Cuadra"
                required: v.boolean(),
                hasCode: v.boolean(),
            })),
            level6: v.optional(v.object({
                name: v.string(), // "Calle", "Subdivisión", "Lote"
                required: v.boolean(),
                hasCode: v.boolean(),
            })),
        }),
        isActive: v.boolean(),
        createdAt: v.string(),
        updatedAt: v.string(),
    }).index("by_country", ["countryCode"]),

    // NUEVA TABLA: Datos jerárquicos de ubicación
    locationData: defineTable({
        countryCode: v.string(), // "GT", "MX", "CO"
        level: v.number(), // 1=Departamento, 2=Municipio, 3=Zona, 4=Colonia
        code: v.optional(v.string()), // Código oficial (ej: "01" para Guatemala)
        name: v.string(), // "Guatemala", "Mixco", "Zona 10"
        parentId: v.optional(v.id("locationData")), // Referencia al nivel superior
        searchKeywords: v.array(v.string()), // ["zona 10", "z10", "z-10"]
        isActive: v.boolean(),
        source: v.string(), // "INE", "manual", "OSM"
        metadata: v.optional(v.object({
            population: v.optional(v.number()),
            area: v.optional(v.number()),
            postalCodes: v.optional(v.array(v.string())),
        })),
        createdAt: v.string(),
        updatedAt: v.string(),
    })
        .index("by_country_level", ["countryCode", "level"])
        .index("by_parent", ["parentId"])
        .index("by_country_parent", ["countryCode", "parentId"])
        .index("by_name", ["name"])
        .index("by_active", ["isActive"])
        .index("by_country_level_parent", ["countryCode", "level", "parentId"]),

    // NUEVA TABLA: Configuración Admin
    adminSettings: defineTable({
        key: v.string(), // "trial_days", "premium_price", etc.
        value: v.union(v.string(), v.number(), v.boolean()),
        description: v.optional(v.string()),
        updatedBy: v.string(), // userId del admin
        updatedAt: v.number(),
    }).index("by_key", ["key"]),

    // Tabla para órdenes/pagos únicos
    orders: defineTable({
        userId: v.string(),
        // Stripe IDs (nuevos)
        stripeOrderId: v.optional(v.string()),
        stripeCustomerId: v.optional(v.string()),
        stripeSessionId: v.optional(v.string()),
        // Polar IDs (compatibilidad)
        polarOrderId: v.optional(v.string()),
        polarCustomerId: v.optional(v.string()),
        
        productName: v.string(),
        amount: v.number(),
        currency: v.string(),
        status: v.string(),
        createdAt: v.number(),
    })
        .index("by_user", ["userId"])
        .index("by_stripe_order", ["stripeOrderId"])
        .index("by_polar_order", ["polarOrderId"]), // Mantener para compatibilidad

    webhookEvents: defineTable({
        type: v.string(),
        // Stripe event ID (nuevo)
        stripeEventId: v.optional(v.string()),
        // Polar event ID (compatibilidad)
        polarEventId: v.optional(v.string()),
        
        createdAt: v.string(),
        modifiedAt: v.string(),
        data: v.any(),
    })
        .index("type", ["type"])
        .index("stripeEventId", ["stripeEventId"])
        .index("polarEventId", ["polarEventId"]), // Mantener para compatibilidad

    amenities: defineTable({
        name: v.string(),
        category: v.string(), // "servicios", "ubicacion", "comodidades", "vistas"
        description: v.optional(v.string()),
        icon: v.optional(v.string()),
        searchKeywords: v.array(v.string()), // Para IA: ["wifi", "internet", "conexion"]
        isActive: v.boolean(),
        sortOrder: v.optional(v.number()),
        createdAt: v.string(),
        updatedAt: v.string(),
    })
        .index("by_category", ["category"])
        .index("by_active", ["isActive"])
        .index("by_sort", ["sortOrder"]),

    // Sistema de mensajería interna
    messages: defineTable({
        // Información del mensaje
        subject: v.string(),
        message: v.string(),
        
        // Relaciones
        propertyId: v.id("properties"), // Propiedad consultada
        senderId: v.string(), // Usuario que envía (tokenIdentifier)
        receiverId: v.string(), // Usuario que recibe (tokenIdentifier)
        
        // Estado del mensaje
        status: v.union(
            v.literal("unread"),    // No leído
            v.literal("read"),      // Leído
            v.literal("replied"),   // Respondido
            v.literal("archived")   // Archivado
        ),
        
        // Información del lead
        leadType: v.union(
            v.literal("inquiry"),       // Consulta general
            v.literal("viewing"),       // Solicitud de visita
            v.literal("offer"),         // Oferta de compra
            v.literal("negotiation")    // Negociación
        ),
        
        // Datos de contacto del interesado
        senderName: v.string(),
        senderEmail: v.string(),
        senderPhone: v.optional(v.string()),
        
        // Timestamps
        createdAt: v.string(),
        readAt: v.optional(v.string()),
        repliedAt: v.optional(v.string()),
        
        // Sistema de créditos (para futuro)
        creditsCharged: v.optional(v.number()),
        creditsPaid: v.optional(v.boolean()),

        // Sistema de respuestas
        isReply: v.optional(v.boolean()), // Si este mensaje es una respuesta
        originalMessageId: v.optional(v.id("messages")), // ID del mensaje original si es respuesta
    })
        .index("by_property", ["propertyId"])
        .index("by_sender", ["senderId"])
        .index("by_receiver", ["receiverId"])
        .index("by_status", ["status"])
        .index("by_lead_type", ["leadType"])
        .index("by_created", ["createdAt"]),

    // Configuración de precios de acciones con créditos
    creditActions: defineTable({
        action: v.string(), // "featured_property", "premium_home", "message_lead"
        cost: v.number(), // Créditos que cuesta
        duration: v.optional(v.number()), // Duración en días (para featured/premium)
        isActive: v.boolean(),
        description: v.string(),
        metadata: v.optional(v.any()), // datos adicionales como límites, etc.
        createdAt: v.number(),
        updatedAt: v.number(),
    }).index("by_action", ["action"])
      .index("by_active", ["isActive"]),

    // Registro de todas las transacciones de créditos
    transactions: defineTable({
        userId: v.string(),
        action: v.string(), // "featured_property", "premium_home", "message_received"
        creditsCost: v.number(),
        description: v.string(),
        
        // Referencias opcionales
        propertyId: v.optional(v.id("properties")),
        messageId: v.optional(v.id("messages")),
        
        // Para servicios con duración
        duration: v.optional(v.number()), // duración en días
        expiresAt: v.optional(v.number()), // timestamp de expiración
        
        // Estado de la transacción
        status: v.union(
            v.literal("active"),    // Servicio activo
            v.literal("expired"),   // Servicio expirado
            v.literal("cancelled")  // Servicio cancelado
        ),
        
        // Timestamps
        createdAt: v.number(),
        updatedAt: v.optional(v.number()),
    })
        .index("by_user", ["userId"])
        .index("by_action", ["action"])
        .index("by_status", ["status"])
        .index("by_expires", ["expiresAt"])
        .index("by_property", ["propertyId"])
        .index("by_user_action", ["userId", "action"])
        .index("by_user_status", ["userId", "status"]),

    // Sistema de conversaciones WhatsApp para reportes
    conversations: defineTable({
        chatId: v.string(), // "<EMAIL>"
        userName: v.string(),
        messageId: v.string(),
        messageType: v.union(v.literal("user"), v.literal("assistant")),
        content: v.string(),
        timestamp: v.string(),

        // Análisis automático
        propertiesDiscussed: v.optional(v.array(v.string())), // IDs de propiedades mencionadas
        interestLevel: v.optional(v.number()), // 1-5 scale
        intent: v.optional(v.string()), // "buscar", "info", "agendar"
        budget: v.optional(v.object({
            min: v.number(),
            max: v.number()
        })),
        preferences: v.optional(v.object({
            type: v.string(),
            zones: v.array(v.string()),
            amenities: v.array(v.string())
        })),

        // Sistema de aprendizaje automático
        analyzed: v.optional(v.boolean()), // true si ya fue analizada por IA
        analysisId: v.optional(v.string()), // ID del análisis que la procesó
        
        // Processing status
        processed: v.boolean(),
        createdAt: v.string(),
    })
        .index("by_chatId", ["chatId"])
        .index("by_messageId", ["messageId"])
        .index("by_timestamp", ["timestamp"])
        .index("by_processed", ["processed"])
        .index("by_interestLevel", ["interestLevel"]),

    // Perfiles de leads desde WhatsApp
    leads: defineTable({
        chatId: v.string(),
        userName: v.optional(v.string()),
        userPhone: v.optional(v.string()),

        // Estado del lead
        status: v.string(), // "new", "interested", "qualified", "converted", "lost"
        source: v.string(), // "whatsapp", "web", "referral"
        assignedAgentId: v.optional(v.string()),

        // Análisis de interés
        interestLevel: v.number(), // 1-5 scale
        lastInteraction: v.string(),
        totalInteractions: v.optional(v.number()),

        // Preferencias extraídas de conversaciones
        budgetMin: v.optional(v.number()),
        budgetMax: v.optional(v.number()),
        preferredType: v.optional(v.string()),
        preferredZones: v.optional(v.array(v.string())),
        amenitiesWanted: v.optional(v.array(v.string())),

        // Seguimiento
        nextFollowUp: v.optional(v.string()),
        followUpReason: v.optional(v.string()),
        priority: v.optional(v.number()), // 1=alta, 2=media, 3=baja

        // Metadata
        notes: v.optional(v.string()),
        tags: v.optional(v.array(v.string())),

        // Timestamps
        createdAt: v.string(),
        updatedAt: v.string(),
    })
        .index("by_chatId", ["chatId"])
        .index("by_status", ["status"])
        .index("by_interestLevel", ["interestLevel"])
        .index("by_lastInteraction", ["lastInteraction"])
        .index("by_nextFollowUp", ["nextFollowUp"])
        .index("by_assignedAgent", ["assignedAgentId"]),

    // === MÓDULO DE AGENDA ===
    
    appointments: defineTable({
        // Información básica
        title: v.string(),
        description: v.optional(v.string()),
        
        // Participantes
        hostId: v.string(), // Usuario que sube propiedades (seller/agent)
        guestId: v.string(), // Usuario interesado (buyer)
        guestName: v.string(),
        guestEmail: v.string(),
        guestPhone: v.optional(v.string()),
        
        // Fechas y horarios
        startTime: v.string(), // ISO string
        endTime: v.string(), // ISO string
        timezone: v.string(),
        
        // Tipo de cita
        type: v.union(
            v.literal("property_viewing"), // Visita a propiedad
            v.literal("consultation"), // Consulta general
            v.literal("negotiation"), // Negociación
            v.literal("document_signing"), // Firma de documentos
            v.literal("other") // Otros
        ),
        
        // Estado
        status: v.union(
            v.literal("scheduled"), // Programada
            v.literal("confirmed"), // Confirmada
            v.literal("completed"), // Completada
            v.literal("cancelled"), // Cancelada
            v.literal("no_show") // No se presentó
        ),
        
        // Información adicional
        location: v.optional(v.string()), // Dirección o lugar
        meetingType: v.union(
            v.literal("in_person"), // Presencial
            v.literal("video_call"), // Videollamada
            v.literal("phone_call") // Llamada telefónica
        ),
        meetingUrl: v.optional(v.string()), // URL de videollamada
        
        // Relaciones
        propertyId: v.optional(v.id("properties")), // Si es relacionada a una propiedad
        
        // Recordatorios
        remindersSent: v.optional(v.boolean()),
        reminder24hSent: v.optional(v.boolean()),
        reminder1hSent: v.optional(v.boolean()),
        
        // Notas
        notes: v.optional(v.string()),
        
        // Timestamps
        createdAt: v.string(),
        updatedAt: v.string(),
        cancelledAt: v.optional(v.string()),
        cancelledBy: v.optional(v.string()),
        cancellationReason: v.optional(v.string()),
    })
        .index("by_host", ["hostId"])
        .index("by_guest", ["guestId"])
        .index("by_property", ["propertyId"])
        .index("by_status", ["status"])
        .index("by_start_time", ["startTime"])
        .index("by_type", ["type"])
        .index("by_host_date", ["hostId", "startTime"]),

    availability: defineTable({
        userId: v.string(), // Usuario que define su disponibilidad
        
        // Configuración de disponibilidad
        dayOfWeek: v.union(
            v.literal(0), // Domingo
            v.literal(1), // Lunes
            v.literal(2), // Martes
            v.literal(3), // Miércoles
            v.literal(4), // Jueves
            v.literal(5), // Viernes
            v.literal(6)  // Sábado
        ),
        
        startTime: v.string(), // "09:00"
        endTime: v.string(), // "17:00"
        
        // Configuración
        isEnabled: v.boolean(),
        timezone: v.string(),
        
        // Slots de tiempo (opcional - para más granularidad)
        slotDuration: v.optional(v.number()), // en minutos, ej: 30, 60
        breakTime: v.optional(v.number()), // tiempo entre citas en minutos
        
        createdAt: v.string(),
        updatedAt: v.string(),
    })
        .index("by_user", ["userId"])
        .index("by_day", ["dayOfWeek"])
        .index("by_user_day", ["userId", "dayOfWeek"]),

    appointmentRequests: defineTable({
        // Solicitud de cita pendiente de confirmación
        hostId: v.string(), // A quien se le solicita la cita
        guestId: v.string(), // Quien solicita la cita
        propertyId: v.optional(v.id("properties")),
        
        // Datos de la solicitud
        requestedStartTime: v.string(),
        requestedEndTime: v.string(),
        message: v.optional(v.string()),
        guestName: v.string(),
        guestEmail: v.string(),
        guestPhone: v.optional(v.string()),
        
        type: v.union(
            v.literal("property_viewing"),
            v.literal("consultation"),
            v.literal("negotiation"),
            v.literal("document_signing"),
            v.literal("other")
        ),
        
        meetingType: v.union(
            v.literal("in_person"),
            v.literal("video_call"),
            v.literal("phone_call")
        ),
        
        status: v.union(
            v.literal("pending"), // Pendiente de respuesta
            v.literal("approved"), // Aprobada (se crea appointment)
            v.literal("rejected"), // Rechazada
            v.literal("expired") // Expirada
        ),
        
        // Respuesta del host
        response: v.optional(v.string()),
        respondedAt: v.optional(v.string()),
        
        // Cita creada (si fue aprobada)
        appointmentId: v.optional(v.id("appointments")),
        
        createdAt: v.string(),
        expiresAt: v.string(), // Auto-expira en X días
    })
        .index("by_host", ["hostId"])
        .index("by_guest", ["guestId"])
        .index("by_status", ["status"])
        .index("by_property", ["propertyId"]),

    // Registro de consumo de créditos por acción específica
    creditConsumptions: defineTable({
        userId: v.string(),
        action: v.string(), // "read_message", "view_appointment_request", etc.
        resourceId: v.string(), // ID del mensaje, solicitud, etc.
        creditsUsed: v.number(),
        description: v.string(),
        timestamp: v.number(),
    })
        .index("by_user", ["userId"])
        .index("by_action", ["action"])
        .index("by_user_action", ["userId", "action"])
        .index("by_resource", ["resourceId"]),

    // Sistema de gestión de System Messages para IA Agent
    systemMessages: defineTable({
        version: v.string(), // "v2.0", "v2.1", etc.
        content: v.string(), // El system message completo
        isActive: v.boolean(), // Solo uno puede estar activo
        createdBy: v.string(), // ID del admin que lo creó
        createdAt: v.number(), // Timestamp
        description: v.optional(v.string()), // Descripción del cambio
        title: v.string(), // Título descriptivo
        tags: v.optional(v.array(v.string())), // Tags para organización
    })
        .index("by_active", ["isActive"])
        .index("by_version", ["version"])
        .index("by_created_by", ["createdBy"])
        .index("by_created_at", ["createdAt"]),



    // AI Agent Knowledge Base
    knowledgeBase: defineTable({
        considerationId: v.string(), // Auto-generated unique ID
        consideration: v.string(), // Free text of the consideration/rule
        category: v.optional(v.string()), // "communication", "search", "appointments", etc.
        priority: v.union(
            v.literal("high"),
            v.literal("medium"),
            v.literal("low")
        ),

        // Detection tracking
        firstDetected: v.string(), // First time it was detected
        lastDetected: v.string(), // Last time it was detected
        frequency: v.number(), // How many times it has been detected

        // Status
        active: v.boolean(), // If it's active for use in the agent
        validated: v.optional(v.boolean()), // If it was validated by a human

        // Metadata
        sourceAnalysis: v.array(v.string()), // IDs of analysis that detected it
        createdAt: v.string(),
        updatedAt: v.string(),
    })
        .index("by_active", ["active"])
        .index("by_category", ["category"])
        .index("by_priority", ["priority"])
        .index("by_frequency", ["frequency"])
        .index("by_first_detected", ["firstDetected"]),

    // ❌ TABLAS ELIMINADAS (no se usaban):
    // - agentImprovementRules
    // - agentMetrics
    // - conversationAnalysis
    // - detectedPatterns
    // - dynamicRules
    // - systemMessageHistory
})
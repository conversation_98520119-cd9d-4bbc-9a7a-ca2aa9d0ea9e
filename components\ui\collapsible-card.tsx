"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronUp, Calendar, User, Building2, Mail } from 'lucide-react';
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

interface CollapsibleCardProps {
  title: string;
  icon?: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  showCount?: number;
  variant?: 'default' | 'compact';
}

export function CollapsibleCard({
  title,
  icon: Icon,
  children,
  defaultExpanded = true,
  className = "",
  headerClassName = "",
  contentClassName = "",
  showCount,
  variant = 'default'
}: CollapsibleCardProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const displayTitle = showCount !== undefined ? `${title} (${showCount})` : title;

  if (variant === 'compact') {
    return (
      <div className={`space-y-2 ${className}`}>
        <div 
          className={`flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors ${headerClassName}`}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center gap-2">
            {Icon && <Icon className="w-4 h-4" />}
            <span className="font-medium text-sm">{displayTitle}</span>
          </div>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            {isExpanded ? (
              <ChevronUp className="h-3 w-3" />
            ) : (
              <ChevronDown className="h-3 w-3" />
            )}
          </Button>
        </div>

        {/* Contenido expandible con animación */}
        <div className={`transition-all duration-300 ease-in-out ${
          isExpanded 
            ? 'opacity-100 max-h-none' 
            : 'opacity-0 max-h-0 overflow-hidden'
        }`}>
          <div className={contentClassName}>
            {children}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardHeader 
          className={`cursor-pointer hover:bg-gray-50 transition-colors ${headerClassName}`}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {Icon && <Icon className="w-5 h-5" />}
              {displayTitle}
            </div>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Contenido expandible con animación */}
      <div className={`transition-all duration-300 ease-in-out ${
        isExpanded 
          ? 'opacity-100 max-h-none' 
          : 'opacity-0 max-h-0 overflow-hidden'
      }`}>
        <div className={contentClassName}>
          {children}
        </div>
      </div>
    </div>
  );
}

// Componente universal para elementos individuales colapsables
interface CollapsibleItemProps {
  id: string;
  data: any;
  type: 'message' | 'request' | 'appointment' | 'property';
  children: React.ReactNode;
  defaultExpanded?: boolean;
  onToggle?: (id: string, expanded: boolean) => void;
}

export function CollapsibleItem({
  id,
  data,
  type,
  children,
  defaultExpanded = false,
  onToggle
}: CollapsibleItemProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  // Sincronizar con defaultExpanded cuando cambie
  React.useEffect(() => {
    setIsExpanded(defaultExpanded);
  }, [defaultExpanded]);

  const handleToggle = React.useCallback(() => {
    const newState = !isExpanded;
    setIsExpanded(newState);
    onToggle?.(id, newState);
  }, [isExpanded, id, onToggle]);

  // Función para obtener información resumida según el tipo - memoizada
  const summary = React.useMemo(() => {
    switch (type) {
      case 'message':
        return {
          title: data.subject || 'Mensaje sin asunto',
          subtitle: `De: ${data.senderName || 'Desconocido'}`,
          badge: getBadgeForLeadType(data.leadType),
          time: data.createdAt ? formatDistanceToNow(new Date(data.createdAt), { addSuffix: true, locale: es }) : '',
          icon: Mail,
          isNew: data.status === 'unread'
        };
      case 'request':
        return {
          title: getTypeText(data.type) || 'Solicitud de cita',
          subtitle: `De: ${data.guestName || 'Desconocido'}`,
          badge: { label: 'Pendiente', color: 'bg-yellow-100 text-yellow-800' },
          time: data.requestedStartTime ? formatDistanceToNow(new Date(data.requestedStartTime), { addSuffix: true, locale: es }) : '',
          icon: Calendar,
          isNew: true
        };
      case 'appointment':
        return {
          title: getTypeText(data.type) || 'Cita programada',
          subtitle: `Con: ${data.guestName || 'Desconocido'}`,
          badge: getStatusBadge(data.status),
          time: data.startTime ? formatDistanceToNow(new Date(data.startTime), { addSuffix: true, locale: es }) : '',
          icon: Calendar,
          isNew: false
        };
      case 'property':
        return {
          title: data.title || 'Propiedad',
          subtitle: `${data.city || ''} • ${data.type || ''}`,
          badge: { label: data.status === 'for_sale' ? 'En Venta' : 'En Alquiler', color: 'bg-blue-100 text-blue-800' },
          time: data.createdAt ? formatDistanceToNow(new Date(data.createdAt), { addSuffix: true, locale: es }) : '',
          icon: Building2,
          isNew: false
        };
      default:
        return {
          title: 'Elemento',
          subtitle: '',
          badge: { label: 'General', color: 'bg-gray-100 text-gray-800' },
          time: '',
          icon: Building2,
          isNew: false
        };
    }
  }, [type, data]);
  const IconComponent = summary.icon;

  // Vista contraída (resumen)
  if (!isExpanded) {
    return (
      <Card
        className="hover:shadow-md transition-all duration-200 cursor-pointer border-l-4 border-l-blue-400 hover:border-l-blue-500"
        onClick={handleToggle}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              {/* Icono */}
              <div className="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center flex-shrink-0">
                <IconComponent className="h-4 w-4 text-gray-600" />
              </div>

              {/* Información resumida */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-gray-900 truncate text-sm">
                    {summary.title}
                  </h4>
                  <Badge className={summary.badge.color}>
                    {summary.badge.label}
                  </Badge>
                  {summary.isNew && (
                    <Badge variant="default" className="bg-blue-600">
                      Nuevo
                    </Badge>
                  )}
                  {type === 'message' && data.status === 'read' && (
                    <Badge variant="default" className="bg-green-600">
                      Leído
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span className="truncate">{summary.subtitle}</span>
                  {summary.time && (
                    <span className="flex items-center gap-1 flex-shrink-0">
                      <Calendar className="w-3 h-3" />
                      {summary.time}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Botón expandir */}
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 flex-shrink-0"
              onClick={(e) => {
                e.stopPropagation();
                handleToggle();
              }}
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Vista expandida (completa)
  return (
    <div className="relative">
      {/* Botón contraer en la esquina superior derecha */}
      <Button
        variant="ghost"
        size="sm"
        className="absolute top-2 right-2 z-10 h-6 w-6 p-0 bg-white/90 hover:bg-white shadow-sm border"
        onClick={handleToggle}
      >
        <ChevronUp className="h-4 w-4" />
      </Button>

      {/* Contenido completo */}
      <div className="pr-10">
        {children}
      </div>
    </div>
  );
}

// Funciones auxiliares
function getBadgeForLeadType(leadType: string) {
  switch (leadType) {
    case 'inquiry':
      return { label: 'Consulta', color: 'bg-blue-100 text-blue-800' };
    case 'viewing':
      return { label: 'Visita', color: 'bg-green-100 text-green-800' };
    case 'offer':
      return { label: 'Oferta', color: 'bg-purple-100 text-purple-800' };
    case 'negotiation':
      return { label: 'Negociación', color: 'bg-orange-100 text-orange-800' };
    default:
      return { label: 'General', color: 'bg-gray-100 text-gray-800' };
  }
}

function getTypeText(type: string) {
  switch (type) {
    case 'property_viewing': return 'Visita de Propiedad';
    case 'consultation': return 'Consulta';
    case 'negotiation': return 'Negociación';
    case 'document_signing': return 'Firma de Documentos';
    case 'other': return 'Otro';
    default: return type;
  }
}

function getStatusBadge(status: string) {
  switch (status) {
    case 'confirmed':
      return { label: 'Confirmada', color: 'bg-green-100 text-green-800' };
    case 'pending':
      return { label: 'Pendiente', color: 'bg-yellow-100 text-yellow-800' };
    case 'cancelled':
      return { label: 'Cancelada', color: 'bg-red-100 text-red-800' };
    case 'completed':
      return { label: 'Completada', color: 'bg-blue-100 text-blue-800' };
    default:
      return { label: 'Desconocido', color: 'bg-gray-100 text-gray-800' };
  }
}

// Hook personalizado para manejar múltiples elementos colapsables
export function useCollapsibleState(initialStates: Record<string, boolean> = {}) {
  const [states, setStates] = useState(initialStates);

  const toggle = (key: string) => {
    setStates(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const setExpanded = (key: string, expanded: boolean) => {
    setStates(prev => ({
      ...prev,
      [key]: expanded
    }));
  };

  const expandAll = () => {
    setStates(prev =>
      Object.keys(prev).reduce((acc, key) => ({
        ...acc,
        [key]: true
      }), {})
    );
  };

  const collapseAll = () => {
    setStates(prev =>
      Object.keys(prev).reduce((acc, key) => ({
        ...acc,
        [key]: false
      }), {})
    );
  };

  return {
    states,
    toggle,
    setExpanded,
    expandAll,
    collapseAll,
    isExpanded: (key: string) => states[key] ?? true
  };
}

"use client";

import { useState, use, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { RichFormattedText } from "@/components/ui/formatted-text";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  ArrowLeft, 
  MapPin, 
  Bed, 
  Bath, 
  Square, 
  Car, 
  Home,
  Phone,
  Mail,
  Building2,
  Heart,
  Shield,
  Wifi,
  Dumbbell,
  TreePine,
  Waves,
  ParkingCircle,
  Zap,
  Camera,
  X,
  Share2
} from "lucide-react";
import { toast } from "sonner";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { useUser } from "@clerk/nextjs";
import { Property } from "@/types/marketplace";
import { AppointmentWidget } from "@/components/widgets/AppointmentWidget";

interface PropertyDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

interface ContactFormData {
  firstName: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

export default function PropertyDetailPage({ params }: PropertyDetailPageProps) {
  const resolvedParams = use(params);
  const { user } = useUser();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showContactForm, setShowContactForm] = useState(false);
  const [contactForm, setContactForm] = useState<ContactFormData>({
    firstName: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  // Obtener datos del usuario actual para precargar formulario
  const currentUser = useQuery(api.users.getCurrentUser);

  // Obtener la propiedad específica
  const property = useQuery(
    api.properties.getPropertyById, 
    resolvedParams.id && resolvedParams.id !== "undefined" ? 
      { id: resolvedParams.id as Id<"properties"> } : 
      "skip"
  );

  // Obtener datos del propietario
  const propertyOwner = useQuery(
    api.users.getUserByToken,
    property ? { tokenIdentifier: property.ownerId } : "skip"
  );

  // Obtener datos del agente si existe
  const propertyAgent = useQuery(
    api.users.getUserByToken,
    property && property.agentId ? { tokenIdentifier: property.agentId } : "skip"
  );

  // Determinar quién es el contacto principal (agente si existe, sino propietario)
  const primaryContact = propertyAgent || propertyOwner;

  const createMessage = useMutation(api.messages.createMessage);



  // Efecto para precargar datos del usuario en el formulario
  useEffect(() => {
    if (currentUser && showContactForm) {
      setContactForm(prev => ({
        ...prev,
        firstName: prev.firstName || currentUser.name || '',
        email: prev.email || currentUser.email || '',
        phone: prev.phone || currentUser.phone || '',
      }));
    }
  }, [currentUser, showContactForm]);

  // Obtener propiedades relacionadas (mismo tipo)
  const relatedProperties = useQuery(
    api.properties.getProperties,
    property ? {
      type: property.type,
      limit: 3
    } : "skip"
  );

  // Validar si el ID es válido
  if (!resolvedParams.id || resolvedParams.id === "undefined") {
    return (
      <div className="container mx-auto px-4 py-8 bg-gray-50 min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-lg">ID de propiedad no válido</p>
        </div>
      </div>
    );
  }

  if (property === undefined) {
    return (
      <div className="container mx-auto px-4 py-8 bg-gray-50 min-h-screen">
        <div className="text-center">
          <p className="text-lg">Cargando propiedad...</p>
        </div>
      </div>
    );
  }

  if (property === null) {
    return (
      <div className="container mx-auto px-4 py-8 bg-gray-50 min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">No encontrada</h1>
          <p className="text-lg">La propiedad que buscas no existe</p>
        </div>
      </div>
    );
  }

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      for_sale: { label: 'En Venta', variant: 'default' as const },
      for_rent: { label: 'En Alquiler', variant: 'secondary' as const },
      sold: { label: 'Vendido', variant: 'destructive' as const },
      rented: { label: 'Alquilado', variant: 'outline' as const },
    };
    
    return statusMap[status as keyof typeof statusMap] || { label: status, variant: 'default' as const };
  };

  const getTypeLabel = (type: string) => {
    const typeMap = {
      house: 'Casa',
      apartment: 'Apartamento',
      office: 'Oficina',
      land: 'Terreno',
      commercial: 'Comercial',
    };
    
    return typeMap[type as keyof typeof typeMap] || type;
  };

  // Funciones para manejar cambios
  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast.error("Debes iniciar sesión para enviar mensajes");
      return;
    }

    // Validaciones personalizadas en español
    if (!contactForm.firstName.trim()) {
      toast.error("Por favor, completa el campo de nombre");
      return;
    }

    if (!contactForm.email.trim()) {
      toast.error("Por favor, completa el campo de email");
      return;
    }

    if (!contactForm.subject.trim()) {
      toast.error("Por favor, selecciona un asunto");
      return;
    }

    if (!contactForm.message.trim()) {
      toast.error("Por favor, completa el campo de mensaje");
      return;
    }

    try {
      // Determinar el tipo de lead basado en el asunto
      let leadType: "inquiry" | "viewing" | "offer" | "negotiation" = "inquiry";
      const subjectLower = contactForm.subject.toLowerCase();
      
      if (subjectLower.includes('visita') || subjectLower.includes('ver')) {
        leadType = "viewing";
      } else if (subjectLower.includes('oferta') || subjectLower.includes('compra')) {
        leadType = "offer";
      } else if (subjectLower.includes('negociar') || subjectLower.includes('precio')) {
        leadType = "negotiation";
      }

      await createMessage({
        propertyId: property._id,
        subject: contactForm.subject,
        message: contactForm.message,
        senderName: contactForm.firstName,
        senderEmail: contactForm.email,
        senderPhone: contactForm.phone,
        leadType: leadType,
      });

      toast.success('¡Mensaje enviado! El propietario recibirá tu consulta.');
      setContactForm({ firstName: '', email: '', phone: '', subject: '', message: '' });
      setShowContactForm(false);
      
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Error al enviar el mensaje. Intenta de nuevo.');
    }
  };

  const statusInfo = getStatusBadge(property.status);

  const getAmenityConfig = (amenity: string) => {
    const amenityConfig: {
      icon: React.ReactNode;
      label: string;
      description: string;
      bgColor: string;
    } = {
      icon: null,
      label: '',
      description: '',
      bgColor: '',
    };

    switch (amenity.toLowerCase()) {
      case 'wifi':
      case 'wi-fi':
        amenityConfig.icon = <Wifi className="h-5 w-5 text-blue-600" />;
        amenityConfig.label = 'Wi-Fi';
        amenityConfig.description = 'Internet';
        amenityConfig.bgColor = 'bg-blue-100';
        break;
      case 'aire_acondicionado':
      case 'aire acondicionado':
        amenityConfig.icon = <Waves className="h-5 w-5 text-purple-600" />;
        amenityConfig.label = 'Aire Acondicionado';
        amenityConfig.description = 'Climatización';
        amenityConfig.bgColor = 'bg-purple-100';
        break;
      case 'seguridad':
      case 'seguridad_24h':
      case 'seguridad_privada':
        amenityConfig.icon = <Shield className="h-5 w-5 text-red-600" />;
        amenityConfig.label = 'Seguridad 24h';
        amenityConfig.description = 'Vigilancia';
        amenityConfig.bgColor = 'bg-red-100';
        break;
      case 'gimnasio':
        amenityConfig.icon = <Dumbbell className="h-5 w-5 text-yellow-600" />;
        amenityConfig.label = 'Gimnasio';
        amenityConfig.description = 'Fitness Center';
        amenityConfig.bgColor = 'bg-yellow-100';
        break;
      case 'parking':
      case 'estacionamiento':
        amenityConfig.icon = <ParkingCircle className="h-5 w-5 text-orange-600" />;
        amenityConfig.label = `${property.parking || 0} Estacionamientos`;
        amenityConfig.description = 'Estacionamientos';
        amenityConfig.bgColor = 'bg-orange-100';
        break;
      case 'piscina':
        amenityConfig.icon = <Wifi className="h-5 w-5 text-blue-600" />;
        amenityConfig.label = 'Piscina';
        amenityConfig.description = 'Swimming Pool';
        amenityConfig.bgColor = 'bg-blue-100';
        break;
      case 'ascensor':
        amenityConfig.icon = <Building2 className="h-5 w-5 text-gray-600" />;
        amenityConfig.label = 'Ascensor';
        amenityConfig.description = 'Elevator';
        amenityConfig.bgColor = 'bg-gray-100';
        break;
      case 'balcon':
      case 'balcón':
        amenityConfig.icon = <Square className="h-5 w-5 text-green-600" />;
        amenityConfig.label = 'Balcón';
        amenityConfig.description = 'Balcony';
        amenityConfig.bgColor = 'bg-green-100';
        break;
      case 'terraza':
        amenityConfig.icon = <Square className="h-5 w-5 text-green-600" />;
        amenityConfig.label = 'Terraza';
        amenityConfig.description = 'Terrace';
        amenityConfig.bgColor = 'bg-green-100';
        break;
      case 'jardin':
      case 'jardín':
        amenityConfig.icon = <Square className="h-5 w-5 text-green-600" />;
        amenityConfig.label = 'Jardín';
        amenityConfig.description = 'Garden';
        amenityConfig.bgColor = 'bg-green-100';
        break;
      case 'lavanderia':
      case 'lavandería':
        amenityConfig.icon = <Wifi className="h-5 w-5 text-cyan-600" />;
        amenityConfig.label = 'Lavandería';
        amenityConfig.description = 'Laundry';
        amenityConfig.bgColor = 'bg-cyan-100';
        break;
      case 'salon_de_eventos':
      case 'salón_de_eventos':
      case 'salon de eventos':
        amenityConfig.icon = <Building2 className="h-5 w-5 text-purple-600" />;
        amenityConfig.label = 'Salón de Eventos';
        amenityConfig.description = 'Eventos sociales';
        amenityConfig.bgColor = 'bg-purple-100';
        break;
      case 'area_social':
      case 'área_social':
      case 'area social':
        amenityConfig.icon = <Building2 className="h-5 w-5 text-indigo-600" />;
        amenityConfig.label = 'Área Social';
        amenityConfig.description = 'Zona común';
        amenityConfig.bgColor = 'bg-indigo-100';
        break;
      case 'areas_verdes':
      case 'áreas_verdes':
      case 'areas verdes':
        amenityConfig.icon = <Square className="h-5 w-5 text-green-600" />;
        amenityConfig.label = 'Áreas Verdes';
        amenityConfig.description = 'Espacios naturales';
        amenityConfig.bgColor = 'bg-green-100';
        break;
      case 'cancha_deportiva':
      case 'cancha deportiva':
        amenityConfig.icon = <Building2 className="h-5 w-5 text-orange-600" />;
        amenityConfig.label = 'Cancha Deportiva';
        amenityConfig.description = 'Deportes';
        amenityConfig.bgColor = 'bg-orange-100';
        break;
      case 'amueblado':
        amenityConfig.icon = <Building2 className="h-5 w-5 text-brown-600" />;
        amenityConfig.label = 'Amueblado';
        amenityConfig.description = 'Incluye muebles';
        amenityConfig.bgColor = 'bg-brown-100';
        break;
      case 'cable':
        amenityConfig.icon = <Wifi className="h-5 w-5 text-purple-600" />;
        amenityConfig.label = 'TV Cable';
        amenityConfig.description = 'Televisión';
        amenityConfig.bgColor = 'bg-purple-100';
        break;
      case 'calefaccion':
      case 'calefacción':
        amenityConfig.icon = <Waves className="h-5 w-5 text-orange-600" />;
        amenityConfig.label = 'Calefacción';
        amenityConfig.description = 'Heating';
        amenityConfig.bgColor = 'bg-orange-100';
        break;
      case 'chimenea':
        amenityConfig.icon = <Waves className="h-5 w-5 text-red-600" />;
        amenityConfig.label = 'Chimenea';
        amenityConfig.description = 'Fireplace';
        amenityConfig.bgColor = 'bg-red-100';
        break;
      default:
        // Formatear amenidades desconocidas: "salon_de_eventos" -> "Salón de Eventos"
        const formatted = amenity
          .replace(/_/g, ' ')
          .replace(/\b\w/g, l => l.toUpperCase());
        amenityConfig.icon = <Building2 className="h-5 w-5 text-gray-600" />;
        amenityConfig.label = formatted;
        amenityConfig.description = '';
        amenityConfig.bgColor = 'bg-gray-100';
    }

    return amenityConfig;
  };

  return (
    <div className="w-full">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 py-4 md:py-6">
        <Link href="/properties" className="flex items-center text-blue-600 hover:text-blue-800 mb-4 font-medium">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Volver a propiedades
        </Link>
      </div>

      <div className="container mx-auto px-4 pb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Columna Principal - Imagen y Detalles */}
          <div className="lg:col-span-2 space-y-6">
            {/* Galería de Imágenes */}
            <Card className="overflow-hidden">
              <div className="relative aspect-[2/1] bg-gray-200">
                {property.images && property.images.length > 0 ? (
                  <Image
                    src={property.images[currentImageIndex]}
                    alt={property.title}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-500">Sin imágenes</p>
                  </div>
                )}
                
                {/* Indicadores de imagen */}
                {property.images && property.images.length > 1 && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                    {property.images.map((_: any, index: any) => (
                      <button
                        key={`image-indicator-${index}`}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-2 h-2 rounded-full backdrop-blur-sm border border-white/30 transition-all ${
                          index === currentImageIndex ? 'bg-white' : 'bg-white/50 hover:bg-white/70'
                        }`}
                      />
                    ))}
                  </div>
                )}

                {/* Navegación de imágenes */}
                {property.images && property.images.length > 1 && (
                  <>
                    <button
                      onClick={() => setCurrentImageIndex(currentImageIndex > 0 ? currentImageIndex - 1 : property.images.length - 1)}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 transition-colors"
                    >
                      <ArrowLeft className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => setCurrentImageIndex(currentImageIndex < property.images.length - 1 ? currentImageIndex + 1 : 0)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 transition-colors"
                    >
                      <ArrowLeft className="h-4 w-4 rotate-180" />
                    </button>
                  </>
                )}

                {/* Contador de imágenes */}
                {property.images && property.images.length > 1 && (
                  <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                    {currentImageIndex + 1} / {property.images.length}
                  </div>
                )}
              </div>
            </Card>

            {/* Información Principal */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-3">
                      {property.title}
                    </h1>
                    <div className="flex items-center text-gray-600 mb-3">
                      <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                      <span className="text-sm md:text-base">
                        {property.address}
                        {property.location?.level2?.name && `, ${property.location.level2.name}`}
                        {property.location?.level1?.name && `, ${property.location.level1.name}`}
                        {property.location?.country?.name && `, ${property.location.country.name}`}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-2 mb-4">
                      <Badge variant={statusInfo.variant}>
                        {statusInfo.label}
                      </Badge>
                      <Badge variant="outline">
                        {getTypeLabel(property.type)}
                      </Badge>
                      {property.featured && (
                        <Badge variant="default" className="bg-yellow-500">
                          Destacado
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-2 ml-4">
                    <Button variant="outline" size="icon" className="flex-shrink-0">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" className="flex-shrink-0">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Características principales */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="text-center p-3 bg-gray-50 rounded-xl">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Bed className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">{property.bedrooms}</div>
                    <div className="text-xs text-gray-600">Habitaciones</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-xl">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Bath className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">{property.bathrooms}</div>
                    <div className="text-xs text-gray-600">Baños</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-xl">
                    <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Square className="h-5 w-5 text-purple-600" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">{property.area}</div>
                    <div className="text-xs text-gray-600">m² Totales</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-xl">
                    <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Car className="h-5 w-5 text-orange-600" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">{property.parking || 0}</div>
                    <div className="text-xs text-gray-600">Estacionamientos</div>
                  </div>
                </div>

                <Separator className="my-6" />

                {/* Descripción */}
                <div>
                  <h2 className="text-xl font-bold text-gray-900 mb-4">Descripción</h2>
                  <RichFormattedText text={property.description} />
                </div>
              </CardContent>
            </Card>

            {/* Amenidades */}
            <Card>
              <CardHeader>
                <CardTitle>Amenidades de la Propiedad</CardTitle>
              </CardHeader>
              <CardContent>
                {property.amenities && property.amenities.length > 0 ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {property.amenities.map((amenity: any, index: any) => {
                      // Mapeo de amenidades a iconos y colores
                      const amenityConfig = getAmenityConfig(amenity);
                      return (
                        <div key={`property-amenity-${amenity}-${index}`} className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                          <div className={`w-10 h-10 ${amenityConfig.bgColor} rounded-full flex items-center justify-center`}>
                            {amenityConfig.icon}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{amenityConfig.label}</div>
                            <div className="text-sm text-gray-600">{amenityConfig.description}</div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Building2 className="h-8 w-8 text-gray-400" />
                    </div>
                    <p className="text-gray-500">No se especificaron amenidades para esta propiedad</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Columna Lateral - Información del Agente y Contacto */}
          <div className="space-y-6">
            {/* Precio y Información Principal */}
            <Card className="sticky top-4">
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">
                    {formatPrice(property.price, property.currency)}
                  </div>
                  <div className="text-gray-600">
                    {property.status === 'for_rent' ? 'por mes' : 'precio final'}
                  </div>
                </div>

                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-xl py-6 text-lg font-semibold mb-4"
                  onClick={() => setShowContactForm(!showContactForm)}
                >
                  <Phone className="h-5 w-5 mr-2" />
                  Contactar Agente
                </Button>

                {/* Información del Agente - OCULTA POR PRIVACIDAD */}
                {/* La información del agente/propietario está oculta para proteger su privacidad */}
              </CardContent>
            </Card>

            {/* Modal de Formulario de Contacto */}
            <Dialog open={showContactForm} onOpenChange={setShowContactForm}>
              <DialogContent className="sm:max-w-md mx-0 p-0 gap-0 max-h-[100vh] md:mx-4 md:p-6 md:gap-6 md:max-h-[90vh] md:rounded-lg
                                       max-w-full h-[100vh] md:h-auto w-full md:w-auto
                                       overflow-hidden md:overflow-visible flex flex-col">
                {/* Header */}
                <div className="flex-shrink-0 bg-white border-b md:border-0 p-4 md:p-0 z-10">
                  <div className="md:mb-4">
                    <DialogTitle className="text-lg md:text-xl font-bold text-left">Contactar Agente</DialogTitle>
                    <DialogDescription className="hidden md:block mt-2">
                      Envía un mensaje directo al agente sobre esta propiedad
                    </DialogDescription>
                  </div>
                </div>
                
                {/* Contenido con scroll en móvil */}
                <div className="flex-1 overflow-y-auto md:overflow-visible p-4 md:p-0 min-h-0">
                  {/* Info de la propiedad en móvil */}
                  <div className="bg-blue-50 p-3 rounded-lg mb-6 md:hidden">
                    <div className="text-sm font-medium text-blue-900 mb-1">{property.title}</div>
                    <div className="text-xs text-blue-700">{formatPrice(property.price, property.currency)}</div>
                  </div>

                  <form id="contact-form" onSubmit={handleContactSubmit} className="space-y-6 pb-4" noValidate>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Nombre completo *
                          {currentUser && currentUser.name && <span className="text-xs text-green-600 ml-2">(Precargado desde tu perfil)</span>}
                        </label>
                        <Input
                          placeholder="Tu nombre completo"
                          value={contactForm.firstName}
                          onChange={(e) => setContactForm(prev => ({ ...prev, firstName: e.target.value }))}
                          className={`rounded-xl h-12 text-base ${currentUser && currentUser.name ? 'bg-green-50 border-green-200' : ''}`}
                        />
                        {currentUser && !currentUser.name && (
                          <p className="text-xs text-amber-600 mt-1">
                            💡 <Link href="/dashboard/profile" className="underline hover:text-amber-800">
                              Actualiza tu perfil
                            </Link> para que tus datos se precarguen automáticamente
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Email *
                          {currentUser && <span className="text-xs text-green-600 ml-2">(Desde tu cuenta)</span>}
                        </label>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          value={contactForm.email}
                          onChange={(e) => setContactForm(prev => ({ ...prev, email: e.target.value }))}
                          className={`rounded-xl h-12 text-base ${currentUser ? 'bg-blue-50 border-blue-200' : ''}`}
                          readOnly={!!currentUser}
                        />
                        {currentUser && (
                          <p className="text-xs text-blue-600 mt-1">
                            📧 Este es el email de tu cuenta. Si necesitas cambiarlo, hazlo desde tu perfil de usuario.
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Teléfono (opcional)
                          {currentUser && currentUser.phone && <span className="text-xs text-green-600 ml-2">(Desde tu perfil)</span>}
                        </label>
                        <Input
                          type="tel"
                          placeholder="+502 1234 5678"
                          value={contactForm.phone}
                          onChange={(e) => setContactForm(prev => ({ ...prev, phone: e.target.value }))}
                          className={`rounded-xl h-12 text-base ${currentUser && currentUser.phone ? 'bg-green-50 border-green-200' : ''}`}
                        />
                        {currentUser && !currentUser.phone && (
                          <p className="text-xs text-gray-500 mt-1">
                            💡 <Link href="/dashboard/profile" className="underline hover:text-gray-700">
                              Agrega tu teléfono en tu perfil
                            </Link> para que se precargue automáticamente
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Asunto *
                        </label>
                        <select
                          value={contactForm.subject}
                          onChange={(e) => setContactForm(prev => ({ ...prev, subject: e.target.value }))}
                          className="w-full rounded-xl h-12 text-base border border-gray-300 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">Selecciona el motivo de tu consulta</option>
                          <option value="Solicitar información general">📋 Solicitar información general</option>
                          <option value="Agendar visita a la propiedad">🏠 Agendar visita a la propiedad</option>
                          <option value="Consultar precio y condiciones">💰 Consultar precio y condiciones</option>
                          <option value="Hacer una oferta">🤝 Hacer una oferta</option>
                          <option value="Negociar precio">💬 Negociar precio</option>
                          <option value="Consultar financiamiento">🏦 Consultar financiamiento</option>
                          <option value="Otro motivo">❓ Otro motivo</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Mensaje *
                        </label>
                        <Textarea
                          placeholder={`Hola, estoy interesado en la propiedad "${property.title}" ubicada en ${property.address}${property.location?.level2?.name ? `, ${property.location.level2.name}` : ''}. Me gustaría obtener más información sobre...`}
                          value={contactForm.message}
                          onChange={(e) => setContactForm(prev => ({ ...prev, message: e.target.value }))}
                          className="rounded-xl min-h-[100px] md:min-h-[120px] resize-none text-base"
                        />
                      </div>
                    </div>
                  </form>
                </div>
                
                {/* Footer fijo en móvil con botones */}
                <div className="flex-shrink-0 bg-white border-t md:border-0 p-4 md:p-0">
                  <div className="flex gap-3">
                    <Button 
                      type="button"
                      variant="outline"
                      onClick={() => setShowContactForm(false)}
                      className="flex-1 rounded-xl h-12 md:h-12 font-medium"
                    >
                      Cancelar
                    </Button>
                    <Button 
                      type="submit" 
                      form="contact-form"
                      className="flex-1 bg-blue-600 hover:bg-blue-700 rounded-xl h-12 md:h-12 font-semibold"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Enviar Mensaje
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            {/* Widget de Agenda */}
            <div className="mt-6">
              <AppointmentWidget
                propertyId={property._id}
                ownerId={property.agentId || property.ownerId}
                ownerName={propertyAgent ? "Agente" : "Propietario"}
                propertyTitle={property.title}
              />
            </div>
          </div>
        </div>

        {/* Propiedades Relacionadas */}
        {relatedProperties && relatedProperties.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8 text-center">
              Propiedades Relacionadas
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedProperties.slice(0, 3).map((relatedProperty: any) => (
                <Card key={relatedProperty._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative aspect-[16/10]">
                    <Link href={`/properties/${relatedProperty._id}`}>
                      {relatedProperty.images && relatedProperty.images.length > 0 ? (
                        <Image
                          src={relatedProperty.images[0]}
                          alt={relatedProperty.title}
                          fill
                          className="object-cover hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="bg-gray-200 h-full flex items-center justify-center hover:bg-gray-300 transition-colors">
                          <p className="text-gray-500">Sin imagen</p>
                        </div>
                      )}
                    </Link>
                    <div className="absolute top-3 left-3">
                      <Badge className="bg-blue-600">
                        {formatPrice(relatedProperty.price, relatedProperty.currency)}
                      </Badge>
                    </div>
                  </div>
                  
                  <CardContent className="p-4">
                    <Link href={`/properties/${relatedProperty._id}`}>
                      <h3 className="font-bold text-lg text-gray-900 mb-2 line-clamp-1 hover:text-blue-600 transition-colors cursor-pointer">
                        {relatedProperty.title}
                      </h3>
                    </Link>
                    <div className="flex items-center text-gray-600 mb-3">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span className="text-sm">{relatedProperty.location?.level2?.name || ''}</span>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="font-bold text-gray-900">{relatedProperty.bedrooms}</div>
                        <div className="text-xs text-gray-600">Habitaciones</div>
                      </div>
                      <div>
                        <div className="font-bold text-gray-900">{relatedProperty.bathrooms}</div>
                        <div className="text-xs text-gray-600">Baños</div>
                      </div>
                      <div>
                        <div className="font-bold text-gray-900">{relatedProperty.area}</div>
                        <div className="text-xs text-gray-600">m²</div>
                      </div>
                    </div>
                    
                    <Link href={`/properties/${relatedProperty._id}`}>
                      <Button className="w-full mt-4 bg-blue-600 hover:bg-blue-700 rounded-xl">
                        Ver Detalles
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 